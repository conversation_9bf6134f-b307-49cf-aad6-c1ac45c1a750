import 'package:bloc/bloc.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/features/home/<USER>/entities/calendar_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_calendar_usecase.dart';
import 'package:storetrack_app/shared/models/result.dart';

import '../../../domain/entities/tasks_request_entity.dart';
import '../../../domain/entities/tasks_response_entity.dart';
import '../../../domain/entities/submit_report_request_entity.dart';
import '../../../domain/entities/submit_report_response_entity.dart';
import '../../../domain/usecases/get_tasks_usecase.dart';
import '../../../domain/usecases/submit_report_usecase.dart';
import 'today_page_state.dart';

class TodayPageCubit extends Cubit<TodayPageState> {
  final GetTasksUseCase _getTasksUseCase;
  final GetCalendarUseCase _getCalendarUseCase;
  final SubmitReportUseCase _submitReportUseCase;

  TodayPageCubit(
    this._getTasksUseCase,
    this._getCalendarUseCase,
    this._submitReportUseCase,
  ) : super(TodayPageInitial());

  Future<void> getData(TasksRequestEntity request) async {
    emit(TodayPageLoading());

    try {
      final results = await Future.wait([
        _getTasksUseCase(request),
        _getCalendarUseCase(GetCalendarParams(
          token: request.token,
          userId: request.userId,
        )),
      ]);

      final tasksResult = results[0] as Result<TasksResponseEntity>;
      final calendarResult = results[1] as Result<CalendarResponseEntity>;

      // We need to check hasFailures on the individual results now
      if (!tasksResult.isSuccess || !calendarResult.isSuccess) {
        // Combine error messages if necessary, or handle individually
        // For simplicity, we'll just emit an error if either fails
        final errorMessage = tasksResult.error?.toString() ??
            calendarResult.error?.toString() ??
            'Unknown error occurred.';
        emit(TodayPageError(errorMessage));
        return;
      }

      // Debug log for calendar data
      logger('Calendar API result success: ${calendarResult.isSuccess}');
      if (calendarResult.isSuccess && calendarResult.data != null) {
        logger('Calendar data available: ${calendarResult.data != null}');
        if (calendarResult.data?.data != null) {
          logger(
              'Calendar info available: ${calendarResult.data?.data?.calendarInfo != null}');
          if (calendarResult.data?.data?.calendarInfo != null) {
            logger(
                'Calendar info count: ${calendarResult.data?.data?.calendarInfo?.length}');

            // Log some calendar info entries for debugging
            // final calendarInfo = calendarResult.data?.data?.calendarInfo;
            // if (calendarInfo != null && calendarInfo.isNotEmpty) {
            //   for (var i = 0; i < calendarInfo.length && i < 5; i++) {
            //     var info = calendarInfo[i];
            //     logger(
            //         'Calendar info $i: timestamp=${info.timestamp}, dollarSymbol=${info.dollarSymbol}, budgetAmount=${info.budgetAmount}');
            //   }
            // }
          }
        }
      } else if (!calendarResult.isSuccess) {
        logger('Calendar API error: ${calendarResult.error}');
      }

      // All succeeded, extract data
      logger('Emitting TodayPageSuccess with calendar data');
      emit(TodayPageSuccess(
        tasksResponse: tasksResult.data!,
        calendarResponse: calendarResult.data,
      ));
    } catch (e) {
      logger("Unexpected error in TodayPageCubit: $e");
      emit(TodayPageError('An unexpected error occurred: ${e.toString()}'));
    }
  }

  void resetState() {
    emit(TodayPageInitial());
  }

  // Submit report for scheduling/rescheduling tasks
  Future<void> submitReport(SubmitReportRequestEntity request) async {
    emit(TodayPageLoading()); // Signal start of operation

    try {
      final Result<SubmitReportResponseEntity> result =
          await _submitReportUseCase.call(request);

      if (result.isSuccess) {
        // After successful submission, refresh the task list and calendar concurrently
        final tasksRequest = TasksRequestEntity(
          deviceUid: request.deviceUid ?? "",
          userId: request.userId ?? "",
          appversion: request.appversion ?? "",
          tasks: const [],
          token: request.token ?? "",
        );

        final calendarParams = GetCalendarParams(
          token: request.token ?? "",
          userId: request.userId ?? "",
        );

        final refreshResults = await Future.wait([
          _getTasksUseCase(tasksRequest),
          _getCalendarUseCase(calendarParams),
        ]);

        final tasksResult = refreshResults[0] as Result<TasksResponseEntity>;
        final calendarResult =
            refreshResults[1] as Result<CalendarResponseEntity>;

        // Check if both operations succeeded
        if (tasksResult.isSuccess && calendarResult.isSuccess) {
          emit(TodayPageSuccess(
            tasksResponse: tasksResult.data!,
            calendarResponse: calendarResult.data,
          ));
        } else {
          // Handle partial failure
          final errorMessage = tasksResult.error?.toString() ??
              calendarResult.error?.toString() ??
              'Failed to refresh data after scheduling.';
          emit(TodayPageError(errorMessage));
        }
      } else {
        emit(TodayPageError(result.error ?? 'Failed to submit report.'));
      }
    } catch (e) {
      logger("Unexpected error in TodayPageCubit submitReport: $e");
      emit(TodayPageError('An unexpected error occurred: ${e.toString()}'));
    }
  }

  // Submit multiple reports for rescheduling multiple tasks
  Future<void> submitMultipleReports(
      List<SubmitReportRequestEntity> requests) async {
    emit(TodayPageLoading()); // Signal start of operation

    int successCount = 0;
    List<String> failedTaskIds = [];
    String? lastErrorMessage;

    // Process each task individually in a loop
    for (int i = 0; i < requests.length; i++) {
      final request = requests[i];

      try {
        final Result<SubmitReportResponseEntity> result =
            await _submitReportUseCase.call(request);

        if (result.isSuccess) {
          successCount++;
        } else {
          failedTaskIds.add(request.taskId ?? 'Unknown');
          lastErrorMessage = result.error?.toString() ??
              'Unknown error occurred while submitting task ${request.taskId}.';
        }
      } catch (e) {
        failedTaskIds.add(request.taskId ?? 'Unknown');
        lastErrorMessage = 'Error submitting task ${request.taskId}: $e';
      }
    }

    // After processing all tasks, refresh the data
    if (successCount > 0 && requests.isNotEmpty) {
      final firstRequest = requests.first;
      final tasksRequest = TasksRequestEntity(
        deviceUid: firstRequest.deviceUid ?? "",
        userId: firstRequest.userId ?? "",
        appversion: firstRequest.appversion ?? "",
        tasks: const [],
        token: firstRequest.token ?? "",
      );

      final calendarParams = GetCalendarParams(
        token: firstRequest.token ?? "",
        userId: firstRequest.userId ?? "",
      );

      final refreshResults = await Future.wait([
        _getTasksUseCase(tasksRequest),
        _getCalendarUseCase(calendarParams),
      ]);

      final tasksResult = refreshResults[0] as Result<TasksResponseEntity>;
      final calendarResult =
          refreshResults[1] as Result<CalendarResponseEntity>;

      if (tasksResult.isSuccess && calendarResult.isSuccess) {
        emit(TodayPageSuccess(
          tasksResponse: tasksResult.data!,
          calendarResponse: calendarResult.data,
        ));

        // If some tasks failed, provide detailed feedback but don't treat as complete error
        // since we successfully refreshed the data
      } else {
        final errorMessage = tasksResult.error?.toString() ??
            calendarResult.error?.toString() ??
            'Failed to refresh data after rescheduling.';
        emit(TodayPageError(errorMessage));
      }
    } else {
      // All failed or no requests
      final errorMessage =
          lastErrorMessage ?? 'Failed to reschedule any tasks.';
      emit(TodayPageError(errorMessage));
    }
  }
}
