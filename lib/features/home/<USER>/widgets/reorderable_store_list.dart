import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:storetrack_app/core/constants/app_assets.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import '../../../../core/utils/snackbar_service.dart';
import '../../../../core/utils/url_launcher_utils.dart';
import '../../../../core/utils/task_grouping.dart';
import '../../../../core/storage/data_manager.dart';
import '../../../../di/service_locator.dart';
import '../constants/action_types.dart';
import '../../data/services/sync_service.dart';
import '../widgets/task_action_button.dart';
import 'store_card.dart';
import 'empty_state.dart';
import 'package:auto_route/auto_route.dart';
import '../../../../config/routes/app_router.gr.dart';

class ReorderableStoreList extends StatefulWidget {
  final List<TaskDetail> tasks;
  final bool isCalendarMode;
  final bool showScheduledDate;
  final bool showTickIndicator;
  final bool showAllDisclosureIndicator;
  final bool permanentlyDisableAllDisclosureIndicator;
  final bool isOpenTask;
  final Function(List<TaskDetail> allItems, List<TaskDetail> selectedItems)?
      onSelectionChanged;
  final bool selectAll;
  final void Function(TaskDetail task)? onTaskTap;
  final bool enableTimeValidation;
  final bool disableTaskNavigation;

  const ReorderableStoreList({
    super.key,
    required this.tasks,
    required this.isCalendarMode,
    this.showScheduledDate = false,
    this.showTickIndicator = false,
    this.showAllDisclosureIndicator = false,
    this.permanentlyDisableAllDisclosureIndicator = false,
    this.isOpenTask = false,
    this.onSelectionChanged,
    this.selectAll = false,
    this.onTaskTap,
    this.enableTimeValidation = false,
    this.disableTaskNavigation = false,
  });

  @override
  State<ReorderableStoreList> createState() => _ReorderableStoreListState();
}

class _ReorderableStoreListState extends State<ReorderableStoreList> {
  late List<TaskDetail> _tasks;
  late List<List<TaskDetail>> _tasksNew;
  late List<Widget> _parentActions;
  final Set<String> _selectedTaskIds = {}; // Track selected task IDs
  final Map<String, bool> _checkboxStates =
      {}; // Track checkbox states for all tasks
  final Map<String, bool> _parentCheckboxStates =
      {}; // Track parent checkbox states separately
  final Map<String, bool> _expandedStates =
      {}; // Track expanded states per store
  late final DataManager _dataManager;

  @override
  void initState() {
    super.initState();
    _dataManager = sl<DataManager>();
    _tasks = List<TaskDetail>.from(widget.tasks);
    // Initialize with default grouping first to avoid LateInitializationError
    _tasksNew = groupTasksByStore(_tasks);
    _buildActionButtons();
    logger('📍 CALLING _initializeCheckboxStates from initState');
    _initializeCheckboxStates();

    // Initialize with selectAll state if needed
    if (widget.selectAll) {
      _doSelectAll(true);
    }

    // Load saved order asynchronously after initial setup
    _loadSavedTaskOrder();
  }

  // Load saved task order asynchronously
  void _loadSavedTaskOrder() async {
    try {
      logger('📖 Loading saved task order...');
      final savedOrder = await _dataManager.getTaskOrder();
      logger('📖 Saved order from storage: $savedOrder');

      if (savedOrder != null && savedOrder.isNotEmpty) {
        final currentOrder = _extractCurrentTaskOrder();
        logger('📋 Current order before applying saved: $currentOrder');

        setState(() {
          _applySavedTaskOrder(savedOrder);
        });

        final finalOrder = _extractCurrentTaskOrder();
        logger('✅ Final order after applying saved: $finalOrder');
      } else {
        logger('ℹ️ No saved order found, using default grouping');
      }
      // If no saved order exists, keep the default grouping already set
    } catch (e) {
      logger('❌ Error loading saved task order: $e');
      debugPrint('Error loading saved task order: $e');
    }
  }

  // Apply saved task order to the current tasks
  void _applySavedTaskOrder(List<String> savedOrder) {
    logger('🔄 APPLYING SAVED ORDER: $savedOrder');

    // Create a map for quick lookup of tasks by ID
    Map<String, TaskDetail> taskMap = {};
    for (var task in _tasks) {
      taskMap[task.taskId.toString()] = task;
    }

    logger('📋 Available task IDs in this instance: ${taskMap.keys.toList()}');

    // Create ordered list based on saved order (global order)
    List<TaskDetail> orderedTasks = [];
    List<String> foundTasks = [];
    List<String> skippedTasks = [];

    // First, add tasks in the saved order, but only if they exist in this instance
    for (String taskId in savedOrder) {
      if (taskMap.containsKey(taskId)) {
        // This task exists in the current instance, add it in the saved order
        orderedTasks.add(taskMap[taskId]!);
        taskMap.remove(taskId); // Remove to avoid duplicates
        foundTasks.add(taskId);
      } else {
        // This task exists in global order but not in current instance (belongs to another instance)
        skippedTasks.add(taskId);
      }
    }

    // Then add any new tasks that weren't in the saved order at the end
    final newTasks = taskMap.values.toList();
    orderedTasks.addAll(newTasks);

    logger('✅ Found tasks in saved order: $foundTasks');
    if (skippedTasks.isNotEmpty) {
      logger(
          '⏭️ Tasks in global order but not in this instance: $skippedTasks');
    }
    if (newTasks.isNotEmpty) {
      logger(
          '🆕 New tasks not in saved order: ${newTasks.map((t) => t.taskId.toString()).toList()}');
    }

    // Update the tasks list and regroup
    _tasks = orderedTasks;
    _tasksNew = groupTasksByStore(_tasks);

    final finalGroupInfo = _tasksNew
        .map((group) => {
              'store': group.isNotEmpty ? group.first.storeName : 'Empty',
              'tasks': group.map((t) => t.taskId.toString()).toList()
            })
        .toList();
    logger('📊 Final grouped structure: $finalGroupInfo');
    logger(
        '📋 Final task order for this instance: ${orderedTasks.map((t) => t.taskId.toString()).toList()}');
  }

  // Extract all task IDs in their current order (including subtask ordering)
  List<String> _extractCurrentTaskOrder() {
    // Use the grouped structure to maintain the correct order
    List<String> taskOrder = [];
    for (var taskGroup in _tasksNew) {
      for (var task in taskGroup) {
        taskOrder.add(task.taskId.toString());
      }
    }
    return taskOrder;
  }

  // Merge current instance task order with global task order
  Future<List<String>> _mergeTaskOrderWithGlobal(
      List<String> currentInstanceOrder) async {
    try {
      // Load existing global task order
      final savedGlobalOrder = await _dataManager.getTaskOrder();

      if (savedGlobalOrder == null || savedGlobalOrder.isEmpty) {
        // No global order exists, use current instance order
        logger('🌐 No global order exists, using current instance order');
        return currentInstanceOrder;
      }

      logger('🌐 Existing global order: $savedGlobalOrder');
      logger('📋 Current instance order: $currentInstanceOrder');

      // Create a set of tasks in the current instance for quick lookup
      final instanceTaskIds = currentInstanceOrder.toSet();

      // Start with a copy of the global order
      List<String> mergedOrder = List<String>.from(savedGlobalOrder);

      // Remove tasks that are in current instance from their old positions
      mergedOrder.removeWhere((taskId) => instanceTaskIds.contains(taskId));

      // Find where to insert the reordered tasks from current instance
      // We'll insert them at the position where the first instance task was in the global order
      int insertionIndex = 0;
      for (int i = 0; i < savedGlobalOrder.length; i++) {
        if (instanceTaskIds.contains(savedGlobalOrder[i])) {
          insertionIndex = i;
          break;
        }
      }

      // Adjust insertion index if some tasks were removed before it
      int removedBefore = 0;
      for (int i = 0; i < insertionIndex && i < savedGlobalOrder.length; i++) {
        if (instanceTaskIds.contains(savedGlobalOrder[i])) {
          removedBefore++;
        }
      }
      insertionIndex = insertionIndex - removedBefore;

      // Insert the current instance tasks in their new order
      mergedOrder.insertAll(insertionIndex, currentInstanceOrder);

      // Add any new tasks from current instance that weren't in global order
      for (String taskId in currentInstanceOrder) {
        if (!savedGlobalOrder.contains(taskId)) {
          // These are genuinely new tasks, they're already included in the insertion above
          logger('🆕 New task found in instance: $taskId');
        }
      }

      logger('🔗 Merged global order: $mergedOrder');
      return mergedOrder;
    } catch (e) {
      logger('❌ Error merging task order with global: $e');
      // Fallback to current instance order if merge fails
      return currentInstanceOrder;
    }
  }

  // Save current task order
  Future<void> _saveCurrentTaskOrder() async {
    try {
      final currentOrder = _extractCurrentTaskOrder();
      logger('💾 SAVING TASK ORDER: $currentOrder');

      // Merge with global order to preserve other instances' orderings
      final mergedOrder = await _mergeTaskOrderWithGlobal(currentOrder);
      await _dataManager.saveTaskOrder(mergedOrder);

      logger(
          '✅ Task order saved successfully (merged global order: $mergedOrder)');
    } catch (e) {
      logger('❌ Error saving task order: $e');
      debugPrint('Error saving task order: $e');
    }
  }

  // Initialize checkbox states for all tasks
  void _initializeCheckboxStates() {
    logger('🔄 INITIALIZING checkbox states...');
    logger('🗺️ _expandedStates before initialization: $_expandedStates');

    _checkboxStates.clear();
    _parentCheckboxStates.clear();
    // Note: Don't clear _expandedStates to preserve expanded state across rebuilds

    for (int i = 0; i < _tasksNew.length; i++) {
      final taskGroup = _tasksNew[i];
      if (taskGroup.isNotEmpty) {
        // Initialize parent checkbox state using a unique key
        String parentKey = 'parent_${taskGroup.first.taskId}';
        _parentCheckboxStates[parentKey] = false;

        // Initialize expanded state if not already set - use store name as stable key
        String storeName = taskGroup.first.storeName ?? 'UnknownStore';
        String expandedKey = 'expanded_store_$storeName';
        if (!_expandedStates.containsKey(expandedKey)) {
          // Default to collapsed - only expand when user explicitly does so
          _expandedStates[expandedKey] = false;
          logger('🏗️ INIT: Setting $expandedKey = false (default collapsed)');
        } else {
          logger(
              '🏗️ PRESERVE: Keeping $expandedKey = ${_expandedStates[expandedKey]} (user-set state)');
        }

        // Initialize individual task checkbox states
        for (var task in taskGroup) {
          _checkboxStates[task.taskId.toString()] = false;
        }
      }
    }

    logger('🗺️ _expandedStates after initialization: $_expandedStates');
    logger('✅ COMPLETED checkbox states initialization');
  }

  // Helper method to get all selected tasks
  List<TaskDetail> _getSelectedTasks() {
    return _tasks
        .where((task) => _selectedTaskIds.contains(task.taskId.toString()))
        .toList();
  }

  // Handle selection change and notify parent
  void _handleSelectionChanged() {
    if (widget.onSelectionChanged != null) {
      // Direct callback for user interactions
      widget.onSelectionChanged!(_tasks, _getSelectedTasks());
    }
  }

  // Handle expansion state change
  void _handleExpansionChanged(String storeName, bool isExpanded) {
    final expandedKey = 'expanded_store_$storeName';
    final previousState = _expandedStates[expandedKey];
    logger(
        '🔄 EXPANSION: $expandedKey changed from $previousState → $isExpanded');

    setState(() {
      _expandedStates[expandedKey] = isExpanded;
    });
  }

  // Select or deselect all tasks - this can be called from props (didUpdateWidget) or user action
  void selectAll(bool selectAll) {
    // For safety, always use post-frame callback when called from didUpdateWidget
    // This ensures we don't call setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _doSelectAll(selectAll);
    });
  }

  // Helper method to perform the actual selection update
  void _doSelectAll(bool selectAll) {
    // We need to update the checkbox states directly without setState here
    // since this might be called during build
    if (selectAll) {
      // Select all tasks
      _selectedTaskIds.clear();
      for (var taskGroup in _tasksNew) {
        if (taskGroup.isNotEmpty) {
          // Select parent checkbox
          String parentKey = 'parent_${taskGroup.first.taskId}';
          _parentCheckboxStates[parentKey] = true;

          // Select all tasks in the group
          for (var task in taskGroup) {
            _selectedTaskIds.add(task.taskId.toString());
            _checkboxStates[task.taskId.toString()] = true;
          }
        }
      }
    } else {
      // Deselect all tasks
      _selectedTaskIds.clear();
      for (var key in _checkboxStates.keys) {
        _checkboxStates[key] = false;
      }
      for (var key in _parentCheckboxStates.keys) {
        _parentCheckboxStates[key] = false;
      }
    }

    // We don't notify the parent here since this is coming from the parent
    // and would create an infinite loop.
    // The parent already knows the selection state.
  }

  // Handle parent checkbox change
  void _handleParentCheckboxChanged(bool? value, TaskDetail parentTask) {
    final bool isChecked = value ?? false;
    HapticFeedback.selectionClick();
    setState(() {
      // Get all subtasks for this parent
      final List<TaskDetail> subtasks = _tasksNew.firstWhere(
        (group) => group.isNotEmpty && group.first.taskId == parentTask.taskId,
        orElse: () => [],
      );

      // Update parent checkbox state using unique parent key
      String parentKey = 'parent_${parentTask.taskId}';
      _parentCheckboxStates[parentKey] = isChecked;

      // Update selection and checkbox state for all subtasks
      for (var task in subtasks) {
        final taskId = task.taskId.toString();
        // Update selection tracking
        if (isChecked) {
          _selectedTaskIds.add(taskId);
        } else {
          _selectedTaskIds.remove(taskId);
        }
        // Update checkbox state
        _checkboxStates[taskId] = isChecked;
      }
    });

    // Notify parent of selection change
    _handleSelectionChanged();
  }

  // Handle subtask checkbox change
  void _handleSubtaskCheckboxChanged(bool? value, TaskDetail subtask) {
    final bool isChecked = value ?? false;
    final String taskId = subtask.taskId.toString();

    HapticFeedback.selectionClick();
    setState(() {
      // Update selection tracking
      if (isChecked) {
        _selectedTaskIds.add(taskId);
      } else {
        _selectedTaskIds.remove(taskId);
      }

      // Update checkbox state
      _checkboxStates[taskId] = isChecked;

      // Find parent task group and update parent checkbox
      for (var taskGroup in _tasksNew) {
        if (taskGroup.any((task) => task.taskId == subtask.taskId)) {
          // Check if all subtasks in this group are selected
          bool allSelected = taskGroup
              .every((task) => _checkboxStates[task.taskId.toString()] == true);

          // Update parent checkbox state using unique parent key
          if (taskGroup.isNotEmpty) {
            String parentKey = 'parent_${taskGroup.first.taskId}';
            _parentCheckboxStates[parentKey] = allSelected;
          }

          break;
        }
      }
    });

    // Notify parent of selection change
    _handleSelectionChanged();
  }

  // --- Action Handlers (moved from unscheduled_page.dart) ---
  void _handleParentActionTap(String actionType, TaskDetail task) async {
    HapticFeedback.lightImpact();
    switch (actionType) {
      case ActionTypes.contactInfo:
        context.router.push(StoreInfoRoute(
          storeId: task.storeId?.toString() ?? '0',
          taskId: task.taskId?.toString() ?? '0',
        ));
        break;
      case ActionTypes.map:
        await _openGoogleMaps(task);
        break;
      case ActionTypes.chatAssistant:
        context.router.push(const AssistantRoute());
        break;
      default:
      // No action needed
    }
  }

  Future<void> _openGoogleMaps(TaskDetail task) async {
    try {
      double? latitude;
      double? longitude;
      if (task.latitude != null && task.longitude != null) {
        latitude = task.latitude!.toDouble();
        longitude = task.longitude!.toDouble();
      }
      if (latitude == null || longitude == null) {
        SnackBarService.warning(
          context: context,
          message: 'Location coordinates not available for this task.',
        );
        return;
      }

      if (mounted) {
        await UrlLauncherUtils.launchGoogleMaps(
          context: context,
          latitude: latitude,
          longitude: longitude,
        );
      }
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Error opening map: ${e.toString()}',
        );
      }
    }
  }

  Future<void> _handleChangeHelper(TaskDetail task) async {
    try {
      logger(' handleChangeHelper: ${task.toJson()}');
      final userId = await _dataManager.getUserId();
      final scheduleId = task.scheduleId?.toString();

      if (!mounted) return;

      if (userId == null || userId.isEmpty) {
        SnackBarService.error(
          context: context,
          message: 'User ID not available. Please log in again.',
        );
        return;
      }

      if (scheduleId == null || scheduleId.isEmpty) {
        SnackBarService.error(
          context: context,
          message: 'Schedule ID not available for this task.',
        );
        return;
      }

      final helperUrl =
          'https://appservice.storetrack.com.au/standalone/Helpers.aspx?&pid=$userId&sid=$scheduleId';

      context.router.push(WebBrowserRoute(
        url: helperUrl,
        title: 'Change Helper',
        onReturn: () {
          SyncService().sync(context: context);
        },
      ));
    } catch (e) {
      if (!mounted) return;
      SnackBarService.error(
        context: context,
        message: 'Error opening change helper: ${e.toString()}',
      );
    }
  }

  void _handleSubtaskActionTap(String actionType, TaskDetail task) async {
    HapticFeedback.lightImpact();
    switch (actionType) {
      case ActionTypes.changeHelper:
        await _handleChangeHelper(task);
        break;
      case ActionTypes.viewDocument:
        context.router.push(TaskFilesRoute(
          storeId: task.storeId ?? 0,
          taskId: task.taskId!.toInt(),
          sectionType: 'documents',
        ));
        break;
      case ActionTypes.viewPos:
        context.router.push(PosRoute(task: task));
        break;
      case ActionTypes.viewNote:
        context.navigateTo(NotesRoute(task: task));
        break;
      case ActionTypes.viewBrief:
        context.router.push(TaskFilesRoute(
          storeId: task.storeId ?? 0,
          taskId: task.taskId!.toInt(),
          sectionType: 'brief',
        ));
        break;
      case ActionTypes.chatAssistant:
        context.router.push(const AssistantRoute());
        break;
      default:
      // No action needed
    }
  }

  void _buildActionButtons() {
    if (widget.isCalendarMode) {
      _parentActions = [
        StatefulBuilder(
          builder: (context, setState) {
            return Checkbox(
              value: false, // This will be overridden in TaskCardNew
              onChanged: (value) {}, // This will be overridden in TaskCardNew
            );
          },
        ),
      ];
    } else {
      _parentActions = [
        TaskActionButton(
          icon: AppAssets.taskStore,
          actionType: ActionTypes.contactInfo,
          onPressed: () {
            var parentTask = _tasksNew.isNotEmpty ? _tasksNew[0][0] : null;
            if (parentTask != null) {
              _handleParentActionTap(ActionTypes.contactInfo, parentTask);
            }
          },
        ),
        TaskActionButton(
          icon: AppAssets.appbarMap,
          actionType: ActionTypes.map,
          onPressed: () {
            var parentTask = _tasksNew.isNotEmpty ? _tasksNew[0][0] : null;
            if (parentTask != null) {
              _handleParentActionTap(ActionTypes.map, parentTask);
            }
          },
        ),
        // TaskActionButton(
        //   icon: AppAssets.taskAssistant,
        //   actionType: ActionTypes.chatAssistant,
        //   onPressed: () {
        //     var parentTask = _tasksNew.isNotEmpty ? _tasksNew[0][0] : null;
        //     if (parentTask != null) {
        //       _handleParentActionTap(ActionTypes.chatAssistant, parentTask);
        //     }
        //   },
        // ),
      ];
    }
  }

  @override
  void didUpdateWidget(covariant ReorderableStoreList oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.tasks != widget.tasks ||
        oldWidget.isCalendarMode != widget.isCalendarMode) {
      _tasks = List<TaskDetail>.from(widget.tasks);
      // Initialize with default grouping first
      _tasksNew = groupTasksByStore(_tasks);
      _buildActionButtons();

      // Preserve checkbox states when tasks are updated
      // Only initialize new tasks, don't reset existing ones
      for (var taskGroup in _tasksNew) {
        if (taskGroup.isNotEmpty) {
          // Initialize parent checkbox state if new
          String parentKey = 'parent_${taskGroup.first.taskId}';
          if (!_parentCheckboxStates.containsKey(parentKey)) {
            _parentCheckboxStates[parentKey] = false;
          }

          // Initialize individual task checkbox states if new
          for (var task in taskGroup) {
            final taskId = task.taskId.toString();
            if (!_checkboxStates.containsKey(taskId)) {
              _checkboxStates[taskId] = false;
            }
          }
        }
      }

      // Load saved order asynchronously after updating tasks
      _loadSavedTaskOrder();
    }

    // Apply select all state if it changed - handle it directly
    if (oldWidget.selectAll != widget.selectAll) {
      _doSelectAll(widget.selectAll);
    }
  }

  void _handleSubtaskReorder(int taskIndex, int oldIndex, int newIndex) {
    logger('🔄 SUBTASK REORDER: TaskIndex=$taskIndex, $oldIndex→$newIndex');
    logger('📊 BEFORE REORDER - Current _expandedStates: $_expandedStates');

    setState(() {
      // Enhanced validation with recovery
      if (taskIndex < 0 || taskIndex >= _tasksNew.length) {
        logger(
            '❌ Invalid taskIndex: $taskIndex (available: 0-${_tasksNew.length - 1})');
        logger(
            '🔍 Available groups: ${_tasksNew.map((g) => g.isNotEmpty ? g.first.storeName : "Empty").toList()}');
        return;
      }

      final taskGroup = _tasksNew[taskIndex];
      if (taskGroup.isEmpty) {
        logger('❌ Task group at index $taskIndex is empty');
        return;
      }

      if (oldIndex < 0 || oldIndex >= taskGroup.length) {
        logger(
            '❌ Invalid oldIndex: $oldIndex (group size: ${taskGroup.length}, available: 0-${taskGroup.length - 1})');
        return;
      }

      if (newIndex < 0 || newIndex > taskGroup.length) {
        logger(
            '❌ Invalid newIndex: $newIndex (group size: ${taskGroup.length}, allowed: 0-${taskGroup.length})');
        return;
      }

      // Handle same position reorder
      if (oldIndex == newIndex) {
        logger(
            'ℹ️ Same position reorder (oldIndex == newIndex: $oldIndex), skipping');
        return;
      }

      if (oldIndex < newIndex) {
        newIndex -= 1;
      }
      final task = taskGroup.removeAt(oldIndex);
      taskGroup.insert(newIndex, task);

      // Update the main tasks list to reflect the changes
      _tasks = _tasksNew.expand((group) => group).toList();

      // Log state after reorder
      final newTaskIds = taskGroup.map((t) => t.taskId.toString()).toList();
      logger('✅ REORDERED Group[$taskIndex]: $newTaskIds');
    });

    // Provide haptic feedback for successful reorder
    HapticFeedback.mediumImpact();

    logger('📊 AFTER REORDER - Current _expandedStates: $_expandedStates');

    // Log all store states after reorder to see what's affected
    logger('📊 ALL STORES STATE AFTER REORDER:');
    for (int i = 0; i < _tasksNew.length; i++) {
      final group = _tasksNew[i];
      if (group.isNotEmpty) {
        final storeName = group.first.storeName ?? 'UnknownStore';
        final expandedKey = 'expanded_store_$storeName';
        final isExpanded = _expandedStates[expandedKey] ?? false;
        logger('  Store[$i]: $storeName → expanded: $isExpanded');
      }
    }

    // Save the new task order after reordering
    logger('💾 Saving task order after subtask reorder...');
    _saveCurrentTaskOrder();
  }

  void _handleTaskReorder(int oldIndex, int newIndex) {
    logger('🔄 TASK GROUP REORDER - OldIndex: $oldIndex, NewIndex: $newIndex');

    // Log current state before reorder
    final groupNames = _tasksNew
        .map((group) =>
            group.isNotEmpty ? group.first.storeName ?? 'Unknown' : 'Empty')
        .toList();
    logger('📋 BEFORE GROUP REORDER: $groupNames');

    setState(() {
      if (oldIndex < newIndex) newIndex--;
      final taskGroup = _tasksNew.removeAt(oldIndex);
      _tasksNew.insert(newIndex, taskGroup);

      // Update the main tasks list to reflect the changes
      _tasks = _tasksNew.expand((group) => group).toList();

      // Log state after reorder
      final newGroupNames = _tasksNew
          .map((group) =>
              group.isNotEmpty ? group.first.storeName ?? 'Unknown' : 'Empty')
          .toList();
      logger('✅ AFTER GROUP REORDER: $newGroupNames');
      final allTaskIds = _extractCurrentTaskOrder();
      logger('📝 ALL TASKS ORDER: $allTaskIds');
    });

    // Provide haptic feedback for successful reorder
    HapticFeedback.mediumImpact();

    // Save the new task order after reordering
    logger('💾 Saving task order after group reorder...');
    _saveCurrentTaskOrder();
  }

  // Helper to build subtask actions for a given subtask
  List<Widget> _buildSubtaskActionsFor(TaskDetail subtask) {
    final permanentlyDisable = widget.permanentlyDisableAllDisclosureIndicator;
    List<Widget> actions = [];

    // Change Helper
    if ((subtask.taskCount ?? 0) > 1 && subtask.teamlead == 1) {
      actions.add(TaskActionButton(
        icon: AppAssets.homeProfile,
        actionType: ActionTypes.changeHelper,
        onPressed: () =>
            _handleSubtaskActionTap(ActionTypes.changeHelper, subtask),
      ));
    }

    // View Document
    if (!permanentlyDisable) {
      actions.add(TaskActionButton(
        icon: AppAssets.taskReport,
        actionType: ActionTypes.viewDocument,
        onPressed: () =>
            _handleSubtaskActionTap(ActionTypes.viewDocument, subtask),
      ));
    }

    // View POS
    if (subtask.posRequired == true && !permanentlyDisable) {
      actions.add(TaskActionButton(
        icon: AppAssets.posIcon,
        actionType: ActionTypes.viewPos,
        onPressed: () => _handleSubtaskActionTap(ActionTypes.viewPos, subtask),
      ));
    }

    // View Note
    if ((subtask.taskNote != null && subtask.taskNote!.isNotEmpty) ||
        (subtask.comment != null && subtask.comment!.isNotEmpty)) {
      actions.add(TaskActionButton(
        icon: Icons.edit_outlined,
        actionType: ActionTypes.viewNote,
        onPressed: () => _handleSubtaskActionTap(ActionTypes.viewNote, subtask),
      ));
    }

    // View Brief
    bool hasBrief = subtask.forms != null &&
        subtask.forms!.isNotEmpty &&
        subtask.forms!.any((form) =>
            form.questions != null &&
            form.questions!.isNotEmpty &&
            form.questions!.any((question) =>
                question.questionBrief != null &&
                question.questionBrief!.isNotEmpty));
    if (hasBrief) {
      actions.add(TaskActionButton(
        icon: Icons.work_outline,
        actionType: ActionTypes.viewBrief,
        onPressed: () =>
            _handleSubtaskActionTap(ActionTypes.viewBrief, subtask),
      ));
    }

    // Chat Assistant
    // if (!permanentlyDisable) {
    //   actions.add(TaskActionButton(
    //     icon: AppAssets.taskAssistant,
    //     actionType: ActionTypes.chatAssistant,
    //     onPressed: () =>
    //         _handleSubtaskActionTap(ActionTypes.chatAssistant, subtask),
    //   ));
    // }

    return actions;
  }

  @override
  Widget build(BuildContext context) {
    // Log current state for debugging (reduced logging)
    logger('🏗️ BUILDING ReorderableStoreList with ${_tasksNew.length} groups');
    logger('🗺️ Current _expandedStates map: $_expandedStates');

    // If there are no tasks, return an empty container
    if (_tasksNew.isEmpty) {
      return const EmptyState(message: 'No unscheduled tasks available');
    }

    List<Widget> listItems = [];
    for (int taskIndex = 0; taskIndex < _tasksNew.length; taskIndex++) {
      final task1 = _tasksNew[taskIndex];

      // Skip empty task groups
      if (task1.isEmpty) {
        logger('⚠️ Skipping empty task group at index $taskIndex');
        continue;
      }

      var task = task1.first;
      final String storeName = task.storeName ?? 'UnknownStore';
      final String keyValue = 'store_$storeName'; // Use stable store-based key

      logger(
          '🏗️ Building Store[$taskIndex]: $storeName, TaskIDs: ${task1.map((t) => t.taskId).toList()}, Key: $keyValue');

      // Build subtask actions for each subtask (assuming all subtasks in a group are similar)
      // If you want to show different actions per subtask, you can pass this list per subtask to StoreCard
      final subTaskActions = _buildSubtaskActionsFor(task1.first);

      listItems.add(
        ReorderableDelayedDragStartListener(
          index: listItems
              .length, // Use listItems.length for the ReorderableListView index
          key: ValueKey('drag_$keyValue'),
          enabled: true,
          child: StoreCard(
            heading: 'Priority tasks',
            key: ValueKey(keyValue),
            task: task1,
            parentActions: _parentActions,
            subTaskActions: subTaskActions,
            isCalendarMode: widget.isCalendarMode,
            showScheduledDate: widget.showScheduledDate,
            showTickIndicator: widget.showTickIndicator,
            showAllDisclosureIndicator: widget.showAllDisclosureIndicator,
            permanentlyDisableAllDisclosureIndicator:
                widget.permanentlyDisableAllDisclosureIndicator,
            isOpenTask: widget.isOpenTask,
            // Pass checkbox states
            initialParentCheckboxValue:
                _parentCheckboxStates['parent_${task.taskId}'] ?? false,
            initialChildCheckboxStates: Map.fromEntries(task1.map((subtask) =>
                MapEntry(subtask.taskId.toString(),
                    _checkboxStates[subtask.taskId.toString()] ?? false))),
            // Pass expanded state - use store name as stable key
            initialExpanded: (() {
              final storeName = task.storeName ?? 'UnknownStore';
              final expandedKey = 'expanded_store_$storeName';
              final isExpanded = _expandedStates[expandedKey] ?? false;
              logger(
                  '📤 REBUILD[$taskIndex] PASSING to $storeName: $expandedKey = $isExpanded');
              if (!isExpanded && _expandedStates.containsKey(expandedKey)) {
                logger(
                    '⚠️ Store $storeName was previously tracked but now false!');
              }
              return isExpanded;
            })(),
            onExpansionChanged: (isExpanded) {
              final storeName = task.storeName ?? 'UnknownStore';
              _handleExpansionChanged(storeName, isExpanded);
            },
            onSubtaskReorder: (oldIndex, newIndex) {
              logger(
                  '📞 SUBTASK REORDER: Group[$taskIndex] ${task.storeName} - $oldIndex→$newIndex');
              _handleSubtaskReorder(taskIndex, oldIndex, newIndex);
            },
            onParentSelectionChanged: (isSelected, parentTask) {
              _handleParentCheckboxChanged(isSelected, parentTask);
            },
            onSubtaskSelectionChanged: (isSelected, subtask) {
              _handleSubtaskCheckboxChanged(isSelected, subtask);
            },
            onSubtaskActionTap: _handleSubtaskActionTap,
            onTaskTap: widget.onTaskTap,
            enableTimeValidation: widget.enableTimeValidation,
            disableTaskNavigation: widget.disableTaskNavigation,
          ),
        ),
      );
    }

    // If we have no valid items, show a message
    if (listItems.isEmpty) {
      return const EmptyState(message: 'No valid tasks to display');
    }

    // Wrap in LayoutBuilder to ensure proper constraints
    return LayoutBuilder(
      builder: (context, constraints) {
        return ReorderableListView(
          physics:
              const ClampingScrollPhysics(), // Keep original scroll physics
          buildDefaultDragHandles: false,
          shrinkWrap: true, // Ensure it takes only the space it needs
          padding:
              const EdgeInsets.symmetric(vertical: 8.0), // Add vertical padding
          onReorderStart: (index) {
            HapticFeedback.mediumImpact();
          },
          onReorder: _handleTaskReorder,
          proxyDecorator: (child, index, animation) {
            // Add a spring animation to the dragged item
            final curvedAnimation = CurvedAnimation(
              parent: animation,
              curve: Curves.easeInOut,
              reverseCurve: Curves.easeInOut,
            );

            return AnimatedBuilder(
              animation: curvedAnimation,
              builder: (context, child) {
                final scale =
                    1.0 + (curvedAnimation.value * 0.02); // Subtle scale effect

                return Material(
                  elevation: 4 * curvedAnimation.value,
                  color: Colors.transparent,
                  shadowColor: Colors.black.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(12),
                  child: Transform.scale(
                    scale: scale,
                    child: child,
                  ),
                );
              },
              child: child,
            );
          },
          children: listItems,
        );
      },
    );
  }
}
